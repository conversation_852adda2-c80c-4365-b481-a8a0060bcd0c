import { useState } from "react";
import { SearchMode } from "../data/types";

export function useSearchMode() {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchMode, setSearchMode] = useState<SearchMode>("segments");
  const [selectedRespondent, setSelectedRespondent] = useState<string | null>(null);

  const handleSearchModeChange = (mode: SearchMode) => {
    setSearchMode(mode);
    setSelectedRespondent(null);
    setSearchTerm("");
  };

  const resetSearchState = () => {
    setSelectedRespondent(null);
    setSearchTerm("");
  };

  return {
    searchTerm,
    setSearchTerm,
    searchMode,
    selectedRespondent,
    setSelectedRespondent,
    handleSearchModeChange,
    resetSearchState,
  };
}
