import Button from "@/components/global/buttons/button";
import {
  Box,
  Flex,
  HStack,
  Image,
  Text,
  VStack,
} from "@chakra-ui/react";

type PreStartScreenProps = {
  onButtonClick?: () => void;
};

export default function PreStartScreen({ onButtonClick }: PreStartScreenProps) {
  return (
    <Flex
      direction={{ base: "column", lg: "row" }}
      flex={1}
      h={{ base: "auto", lg: "100%" }}
      minH="100vh"
      justifyContent="center"
      alignItems="center"
      bgColor={"rgb(36,35,35)"}
      gap={0}
      position={"relative"}
      overflowY="auto"
      overflowX={"hidden"}
    >
      <Image
        src="/images/logoBancoABC.svg"
        alt="Banco ABC"
        w={{ base: "3rem", md: "4rem", lg: "5rem" }}    // escala com zoom
        h="auto"
        position="absolute"
        top={{ base: "1rem", md: "1.5rem", lg: "2rem" }} // unidades relativas
        left={{ base: "1rem", md: "1.5rem", lg: "2rem" }}
      />

      <VStack
        mt={20}
        w={{ base: "100%", lg: "50%" }}
        h="auto"
        justify={"center"}
        px={{ base: 6, md: 10, lg: 20 }}
        pt={{ base: 24, lg: 25 }}
        pb={{ base: 10, lg: 15 }}
      >
        <VStack gap={{ base: 5, "2xl": 8 }} alignItems="start">
          <Text
            fontSize={{ base: 24, "2xl": 26 }}
            fontWeight="light"
            color={"white"}
          >
            Aqui, você encontra um verdadeiro mapa estratégico para o seu
            crescimento profissional.
          </Text>
          <Text
            fontSize={{ base: 16, "2xl": 20 }}
            fontWeight="medium"
            color={"white"}
          >
            Você está prestes a iniciar um assessment cuidadosamente projetado
            para mapear suas competências e impulsionar seu crescimento. Serão
            30 questões situacionais, desenvolvidas para refletir os desafios
            reais do seu dia a dia como Comercial.
          </Text>
          <Text
            fontSize={{ base: 16, "2xl": 20 }}
            fontWeight="medium"
            color={"white"}
          >
            O preenchimento pode ser feito
            no seu tempo. Você poderá retomar o questionário do ponto onde parou.
          </Text>
          <Text
            fontSize={{ base: 16, "2xl": 20 }}
            fontWeight="medium"
            color={"white"}
            display={{ base: "none", lg: "block" }}
          >
            Reserve cerca de 45 a 60  minutos para responder às perguntas com calma e
            atenção. Comece agora sua jornada de evolução.
          </Text>
          <Button
            w={{ base: 32, lg: 40 }}
            h={{ base: 12, lg: 14 }}
            borderRadius={20}
            fontSize={{ base: 16, lg: 24 }}
            fontWeight={"extrabold"}
            display={{ base: "none", lg: "block" }}
            onClick={onButtonClick}
          >
            Começar
          </Button>
        </VStack>
      </VStack>

      <VStack
        w={{ base: "100%", lg: "50%" }}
        h="auto"
        justify={"center"}
        align={"center"}
        gap={8}
        pt={{ base: 0, lg: 15 }}
        pb={{ base: 10, lg: 15 }}
        px={{ base: 6, md: 10, "2xl": 0 }}
      >
        <Text
          fontSize={{ base: "20px", "2xl": "32px" }}
          fontWeight="light"
          color={"white"}
        >
          O que esperar do resultado?
        </Text>

        <Flex
          direction={{ base: "column", md: "row" }}
          w={"100%"}
          justifyContent="space-around"
          px={{ base: 0, "2xl": 20 }}
          gap={5}
        >
          <VStack w={{ base: "100%", md: "50%" }} alignItems="start" gap={2}>
            <Box position="relative" w="100%">
              <Image
                src="/images/feedback-img.png"
                alt="Banco ABC"
                w="100%"
                h="auto"
                borderRadius={20}
              />
              <Box
                position="absolute"
                bottom="0"
                left={{ base: 14, "2xl": 16 }}
                w="100%"
                p={{ base: 3, "2xl": 5 }}
                borderBottomRadius={20}
              >
                <Text
                  color="white"
                  fontSize={{ base: "lg", "2xl": "2xl" }}
                  fontWeight="bold"
                >
                  Feedback Personalizado
                </Text>
              </Box>
            </Box>
            <Text color="white" fontSize={{ base: "sm", "2xl": "md" }}>
              Descubra seus pontos fortes e identifique áreas de desenvolvimento
              com clareza.
            </Text>
          </VStack>

          <VStack w={{ base: "100%", md: "50%" }} alignItems="start" gap={2}>
            <Box position="relative" w="100%">
              <Image
                src="/images/feedforward-img.png"
                alt="Banco ABC"
                w="100%"
                h="auto"
                borderRadius={20}
              />
              <Box
                position="absolute"
                bottom="0"
                left={{ base: 14, "2xl": 16 }}
                w="100%"
                p={{ base: 3, "2xl": 5 }}
                borderBottomRadius={20}
              >
                <Text
                  color="white"
                  fontSize={{ base: "lg", "2xl": "2xl" }}
                  fontWeight="bold"
                >
                  Feedforward Estratégico
                </Text>
              </Box>
            </Box>

            <VStack gap={5} alignItems={"start"}>
              <Text color="white" fontSize={{ base: "sm", "2xl": "md" }}>
                Receba recomendações de aprendizado e desenvolvimento.
              </Text>

              {/* visível apenas no mobile */}
              <Text
                fontSize={{ base: 16, "2xl": 24 }}
                fontWeight="medium"
                color={"white"}
                display={{ base: "block", lg: "none" }}
              >
                Reserve cerca de 45 a 60  minutos para responder às perguntas com calma e
                atenção. Comece agora sua jornada de evolução.
              </Text>
              <Button
                w={{ base: 32, lg: 40 }}
                h={{ base: 12, lg: 14 }}
                borderRadius={20}
                fontSize={{ base: 16, lg: 24 }}
                fontWeight={"extrabold"}
                display={{ base: "block", lg: "none" }}
                onClick={onButtonClick}
              >
                Começar
              </Button>
            </VStack>
          </VStack>
        </Flex>
      </VStack>
    </Flex>
  );
}
