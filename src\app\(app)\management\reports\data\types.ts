export interface Cargo {
  secureId: string;
  cargo_nome: string;
  createdAt: string;
  updatedAt: string;
}

export interface Gerencia {
  secureId: string;
  gerencia_nome: string;
  cargos: Cargo[];
}

export interface Superintendencia {
  secureId: string;
  superintendencia_nome: string;
  gerencias: Gerencia[];
}

export interface Segment {
  secureId: string;
  segmento_nome: string;
  superintendencias: Superintendencia[];
}

export interface ReportsData {
  data: Segment[];
}

// Hook return types
export interface UseHierarchicalSelectionReturn {
  selectedItems: Set<string>;
  allSelected: boolean;
  handleItemChange: (id: string, checked: boolean) => void;
  handleItemChangeByPath: (
    segmentId: string,
    superintendenciaId?: string,
    gerenciaId?: string,
    cargoId?: string,
    checked?: boolean
  ) => void;
  handleAllChange: (checked: boolean) => void;
  isItemSelected: (id: string) => boolean;
  isItemSelectedByPath: (
    segmentId: string,
    superintendenciaId?: string,
    gerenciaId?: string,
    cargoId?: string
  ) => boolean;
}

export interface UseRoundSelectionReturn {
  selectedRounds: Set<string>;
  handleRoundChange: (round: string, checked: boolean) => void;
}

export interface UseSearchFilterReturn {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

// Report Generation Types
export interface ReportFilter {
  segment: string;
  superintendency: string;
  management: string;
  position: string;
}

export interface ReportGenerationRequest {
  rounds: "checkIn" | "checkOut" | "both";
  filters: ReportFilter[];
}

// Respondent Report Generation Types
export interface RespondentReportGenerationRequest {
  rounds: "checkIn" | "checkOut" | "both";
  respondentSecureId: string;
}

// Search Mode Types
export type SearchMode = "respondent" | "segments";

export interface SearchModeOption {
  value: SearchMode;
  label: string;
}

export interface ProficiencyData {
  average: number;
  count: number;
  "proficiency-1": number;
  "proficiency-2": number;
  "proficiency-3": number;
  "proficiency-4": number;
  "proficiency-5": number;
}

export interface AverageDimension {
  name: string;
  bgColor: string;
  barsColor: string;
  checkIn: ProficiencyData;
  checkOut?: ProficiencyData;
}

export interface Variable {
  name: string;
  dimension: string;
  foundation: string;
  bgColor: string;
  barsColor: string;
  checkIn: ProficiencyData;
  checkOut?: ProficiencyData;
}

export interface FoundationReportData {
  name: string;
  dimension: string;
  bgColor: string;
  barsColor: string;
  checkIn: ProficiencyData;
  checkOut?: ProficiencyData;
}

export interface GeneralProficiencyData {
  checkIn: ProficiencyData;
  checkOut?: ProficiencyData;
  bgColor: string;
  barsColor: string;
}


export interface ReportGenerationResponse {
  respondents: {
    checkIn: {
      quantityAnswered: number;
      quantityAvailable: number;
    };
    checkOut: {
      quantityAnswered: number;
      quantityAvailable: number;
    };
    names: string[];
    checkInDate?: string;
    checkOutDate?: string;
  },
  averageCheckInCheckOut: {
    checkIn: number;
    checkOut?: number;
  };
  generalProficiency: GeneralProficiencyData;
  averageDimensions: AverageDimension[];
  averageFoundations: FoundationReportData[];
  variables: Variable[];
  reportText: ReportTextData | null;
}

export interface UseReportGenerationReturn {
  generateReport: (data: ReportGenerationRequest) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  reportData: ReportGenerationResponse | null;
}

export interface ReportTextData {
  level: string;
  score: number;
  text: string;
  generatedAt: string;
}
