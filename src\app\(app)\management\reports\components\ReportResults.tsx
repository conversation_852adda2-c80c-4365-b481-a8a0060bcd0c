import {
  <PERSON>,
  VStack,
  H<PERSON>tack,
  Text,
  Image,
  Flex,
  <PERSON>ack,
  Button,
} from "@chakra-ui/react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  LabelList,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts";
import {
  ReportGenerationResponse,
  SearchMode,
  Variable,
  ProficiencyData,
} from "../data/types";
import { formatInTimeZone } from "date-fns-tz";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { useRef, useState } from "react";
import DefaultButton from "@/components/global/buttons/button";
import { capitalizeSentence } from "@/utils/funcs/capitalize-sentence";

interface ReportResultsProps {
  reportData: ReportGenerationResponse;
  searchMode: SearchMode;
}

export function ReportResults({ reportData, searchMode }: ReportResultsProps) {
  const reportRef = useRef<HTMLDivElement>(null);
  const [isLoadingPdf, setIsLoadingPdf] = useState(false);

  const darkenColor = (hex: string, percent: number): string => {
    const factor = 1 - percent / 100;
    const channels = hex.slice(1).match(/.{2}/g) || [];
    const newHex = channels
      .map((ch) => {
        const newChannel = Math.floor(parseInt(ch, 16) * factor);
        // Garante que o valor não seja negativo e formata para 2 dígitos hexadecimais
        return ("0" + Math.max(0, newChannel).toString(16)).slice(-2);
      })
      .join("");
    return `#${newHex}`;
  };

  const handleGeneratePdf = async () => {
    const element = reportRef.current;
    if (!element) return;

    setIsLoadingPdf(true);

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      backgroundColor: "#1A202C",
    });

    const imgData = canvas.toDataURL("image/png");
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;

    const aspectRatio = canvasWidth / canvasHeight;

    const pdfWidth = 210;
    const pdfHeight = pdfWidth / aspectRatio;

    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: [pdfWidth, pdfHeight],
    });

    pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);

    const fileName = `relatorio-${
      searchMode === "respondent" ? reportData?.respondents.names[0] : "Geral"
    }.pdf`;
    pdf.save(fileName);

    setIsLoadingPdf(false);
  };

  // Transform API data to chart format
  const transformToChartData = (data: any) => {
    const { checkIn, checkOut } = data;

    // Lógica para respondente único: mostra UMA barra baseada na MÉDIA.
    if (checkIn.count === 1) {
      // Descobre em qual nível de proficiência a média se encontra (1 a 5)
      const checkinProficiencyLevel = Math.floor(checkIn.average / 100);
      const checkoutProficiencyLevel =
        checkOut && checkOut.count === 1
          ? Math.floor(checkOut.average / 100)
          : 0;

      // Cria os dados do gráfico
      return Array.from({ length: 5 }, (_, i) => {
        const level = i + 1;
        return {
          name: `${level}`,
          // A barra 'checkin' só terá valor se o nível for o correto.
          // O valor '1' é apenas para a barra aparecer. O rótulo mostrará o valor real.
          checkin: level === checkinProficiencyLevel ? 1 : 0,
          // O LabelList precisa de um valor para mostrar, então usamos a média real.
          checkinLabel:
            level === checkinProficiencyLevel
              ? (checkIn.average / 100).toFixed(2)
              : undefined,
          // Lógica similar para o checkout
          checkout: level === checkoutProficiencyLevel ? 1 : 0,
          checkoutLabel:
            level === checkoutProficiencyLevel && checkOut
              ? (checkOut.average / 100).toFixed(2)
              : undefined,
        };
      });
    }

    // Lógica para múltiplos respondentes (como era antes): mostra a DISTRIBUIÇÃO.
    return Array.from({ length: 5 }, (_, i) => {
      const proficiencyKey = `proficiency-${i + 1}` as const;
      return {
        name: `${i + 1}`,
        checkin: checkIn[proficiencyKey],
        checkout: checkOut ? checkOut[proficiencyKey] : undefined,
      };
    });
  };

  const transformToPercentageData = (dimension: any) => {
    // Pega o número total de respondentes para check-in e check-out
    const totalCheckIn = dimension.checkIn.count;
    const totalCheckOut = dimension.checkOut ? dimension.checkOut.count : 0;

    // Mapeia os 5 níveis de proficiência
    return Array.from({ length: 5 }, (_, i) => {
      const proficiencyKey = `proficiency-${i + 1}` as const;

      // Calcula a porcentagem para check-in (evita divisão por zero)
      const checkinPercentage =
        totalCheckIn > 0
          ? (dimension.checkIn[proficiencyKey] / totalCheckIn) * 100
          : 0;

      // Calcula a porcentagem para check-out, se houver dados
      const checkoutPercentage =
        totalCheckOut > 0 && dimension.checkOut
          ? (dimension.checkOut[proficiencyKey] / totalCheckOut) * 100
          : undefined;

      return {
        name: `${i + 1}`, // Nível de proficiência (1 a 5)
        checkin: checkinPercentage,
        checkout: checkoutPercentage,
      };
    });
  };

  // Formata o valor do rótulo para incluir o símbolo de porcentagem (%)
  const formatLabelAsPercentage = (label: React.ReactNode) => {
    if (typeof label === "number") {
      return `${label.toFixed(0)}%`;
    }
    if (typeof label === "string" && !isNaN(Number(label))) {
      return `${Number(label).toFixed(0)}%`;
    }
    return "";
  };

  // Helper function to check if checkout data exists
  const hasCheckoutData = (data: any) => {
    return data.checkOut && data.checkOut.count > 0;
  };

  // Helper function to check if the report has any checkout data
  const reportHasCheckoutData = () => {
    if (reportData) {
      // Check if checkout exists in the main averages
      if (
        reportData.averageCheckInCheckOut?.checkOut !== undefined &&
        reportData.averageCheckInCheckOut.checkOut !== null
      ) {
        return true;
      }
      // Check if any dimension has checkout data
      return (
        reportData.averageDimensions?.some((dimension) =>
          hasCheckoutData(dimension)
        ) || false
      );
    }
    return true; // Default to true for example data
  };

  // Single Result view: Show when user searches by respondent
  // Multiple Results view: Show when user searches by segments/positions
  return (
    <>
      <Box p={4} textAlign="right">
        <DefaultButton
          onClick={handleGeneratePdf}
          loading={isLoadingPdf}
          loadingText="Gerando PDF..."
          color={"white"}
          bg={"#a6864a"}
          size={"lg"}
        >
          Baixar Relatório em PDF
        </DefaultButton>
      </Box>

      <Box
        ref={reportRef}
        w="100%"
        position="relative"
        overflow="hidden"
        flexShrink={0}
        bg={"rgb(35,34,34)"}
        color={"white"}
      >
        {searchMode === "respondent" ? (
          <Box position="relative" overflow="hidden" flexShrink={0}>
            <VStack w={"100%"} gap={6} align="stretch" p={6}>
              <Box position="relative" w="full">
                <Box
                  position="absolute"
                  top={0}
                  right={0}
                  w={{ base: "0", lg: "300px" }} // "janela" visível.
                  h="100%"
                  overflow="hidden"
                  display={{ base: "none", lg: "block" }}
                  hideBelow={"xl"}
                >
                  <Image
                    src="/images/padraoBG-01.svg"
                    alt="Detalhe de fundo"
                    w="850px" // "zoom" da imagem
                    h="auto"
                    position="absolute"
                    top="0"
                    right="0"
                    maxW="none"
                  />
                </Box>

                {/* Header Section */}
                <Stack
                  gap={6}
                  flexDirection={{ base: "column", lg: "row" }}
                  align={{ base: "start", lg: "center" }}
                >
                  <Image
                    src="/images/logoBancoABC.svg"
                    alt="Banco ABC"
                    w={{ base: "70px", lg: "100px" }}
                    h="auto"
                  />
                  <Stack
                    gap={4}
                    w={{ base: "100%", lg: "auto" }}
                    align={"center"}
                    justifyContent={"center"}
                    flexShrink={0}
                  >
                    <HStack
                      w={"100%"}
                      justifyContent={"center"}
                      flexWrap={{ base: "wrap", lg: "nowrap" }}
                      gap={2}
                    >
                      <Box
                        bgColor={"gray.300"}
                        color={"black"}
                        borderRadius={"lg"}
                        p={3}
                      >
                        <Text color={"black"} fontSize={"md"}>
                          Nome: {reportData?.respondents.names[0]}
                        </Text>
                      </Box>
                      <Box
                        bgColor={"gray.300"}
                        color={"black"}
                        borderRadius={"lg"}
                        p={3}
                      >
                        <Text color={"black"} fontSize={"md"}>
                          Check-in:{" "}
                          {reportData?.respondents.checkInDate
                            ? formatInTimeZone(
                                reportData?.respondents.checkInDate,
                                "America/Sao_Paulo",
                                "dd/MM/yyyy HH:mm:ss"
                              )
                            : "Indisponível"}
                        </Text>
                      </Box>
                      {reportHasCheckoutData() && (
                        <Box
                          bgColor={"gray.300"}
                          color={"black"}
                          borderRadius={"lg"}
                          p={3}
                        >
                          <Text color={"black"} fontSize={"md"}>
                            Check-out:{" "}
                            {reportData?.respondents.checkOutDate
                              ? formatInTimeZone(
                                  reportData?.respondents.checkOutDate,
                                  "America/Sao_Paulo",
                                  "dd/MM/yyyy HH:mm:ss"
                                )
                              : "Indisponível"}
                          </Text>
                        </Box>
                      )}
                    </HStack>
                  </Stack>
                </Stack>

                {/* Main Content Section */}
                <Stack
                  gap={20}
                  px={0}
                  align="stretch"
                  flexDirection={{ base: "column", lg: "row" }}
                  w="100%"
                >
                  <VStack
                    gap={8}
                    py={8}
                    justifyContent="space-around"
                    flex={{ base: "1", lg: "1.5" }}
                    w={{ base: "100%", lg: "auto" }}
                    minW={{ lg: "400px" }}
                  >
                    <Box
                      bg={"#a6864a"}
                      px={{ base: 6, lg: 10 }}
                      py={{ base: 4, lg: 6 }}
                      borderRadius={"4xl"}
                      w="100%"
                    >
                      <Text
                        color={"white"}
                        fontSize={{ base: "md", lg: "lg" }}
                        textAlign={"center"}
                      >
                        {reportData.reportText?.text}
                      </Text>
                    </Box>
                    <VStack
                      gap={4}
                      w="100%"
                      align={"center"}
                      justifyContent={"center"}
                    >
                      <HStack
                        w={"100%"}
                        justifyContent={"center"}
                        flexWrap="wrap"
                        gap={2}
                      >
                        <Box
                          bgColor={"gray.300"}
                          color={"black"}
                          borderRadius={"lg"}
                          p={3}
                        >
                          <Text
                            color={"black"}
                            fontSize={{ base: "sm", lg: "md" }}
                          >
                            Check-in- Proficiência Geral:{" "}
                          </Text>
                        </Box>
                        <Box
                          color={"white"}
                          fontSize={"md"}
                          border={"2px solid #a6864a"}
                          borderRadius={"lg"}
                          p={2}
                        >
                          <Text
                            color={"white"}
                            fontSize={{ base: "md", lg: "xl" }}
                          >
                            {(
                              reportData?.averageCheckInCheckOut?.checkIn / 100
                            ).toFixed(2)}
                          </Text>
                        </Box>
                      </HStack>
                      {reportHasCheckoutData() && (
                        <HStack
                          w={"100%"}
                          justifyContent={"center"}
                          flexWrap="wrap"
                          gap={2}
                        >
                          <Box
                            bgColor={"gray.300"}
                            color={"black"}
                            borderRadius={"lg"}
                            p={3}
                          >
                            <Text
                              color={"black"}
                              fontSize={{ base: "sm", lg: "md" }}
                            >
                              Check-out- Proficiência Geral:{" "}
                            </Text>
                          </Box>
                          <Box
                            color={"white"}
                            fontSize={"md"}
                            border={"2px solid #a6864a"}
                            borderRadius={"lg"}
                            p={2}
                          >
                            <Text
                              color={"white"}
                              fontSize={{ base: "md", lg: "xl" }}
                            >
                              {reportData?.averageCheckInCheckOut?.checkOut
                                ? (
                                    reportData?.averageCheckInCheckOut
                                      .checkOut / 100
                                  ).toFixed(2)
                                : 0}
                            </Text>
                          </Box>
                        </HStack>
                      )}
                    </VStack>
                    <Text
                      color={"white"}
                      fontSize={{ base: "md", lg: "lg" }}
                      textAlign={"center"}
                    >
                      "Este relatório mostra seu nível de proficiência em dois
                      momentos: Check-in, que é o diagnóstico inicial realizado
                      na primeira aplicação do assessment, e Check-out, que será
                      feito após sua participação nos programas de capacitação
                      recomendados. A comparação entre os dois momentos permite
                      acompanhar sua evolução e direcionar seu desenvolvimento
                      com mais precisão."
                    </Text>
                  </VStack>
                  <Box
                    flex={{ base: "none", lg: "1" }}
                    w={{ base: "80%", md: "60%", lg: "auto" }}
                    maxW={{ base: "400px", lg: "600px" }}
                    zIndex={1}
                    alignSelf={"center"}
                  >
                    <Image
                      src="/images/mandala.png"
                      alt="Mandala"
                      w="100%"
                      h="auto"
                      zIndex={1}
                    />
                  </Box>
                </Stack>
              </Box>
              {/* General Proficiency Chart */}
              {(() => {
                // const generalProficiency = calculateGeneralProficiency();
                const generalProficiency = reportData.generalProficiency;
                const hasCheckout = hasCheckoutData(generalProficiency);

                return (
                  <Box px={{ base: 3, md: 6 }} w="full">
                    <Stack
                      w={"100%"}
                      bg={generalProficiency.bgColor}
                      borderRadius="4xl"
                      gap={0}
                      align="stretch"
                      flexDirection={{ base: "column", lg: "row" }}
                    >
                      <Flex
                        w="100%"
                        color="white"
                        justify={{ base: "center", lg: "flex-start" }}
                        align="center"
                        gap={{ base: 2, sm: 4 }}
                        py={{ base: 2, lg: 0 }}
                        px={{ base: 0, lg: 6 }}
                      >
                        <HStack>
                          <Text fontSize={{ base: "sm", lg: "lg" }}>
                            Proficiência Geral:
                          </Text>
                          <Box
                            border="1px solid white"
                            borderRadius="md"
                            px={1}
                            py={1}
                          >
                            <Text fontSize={{ base: "sm", lg: "lg" }}>
                              {(
                                generalProficiency.checkIn.average / 100
                              ).toFixed(2)}
                            </Text>
                          </Box>
                          {hasCheckout && (
                            <Box
                              border="1px solid white"
                              borderRadius="md"
                              px={1}
                              py={1}
                            >
                              <Text fontSize={{ base: "sm", lg: "lg" }}>
                                {generalProficiency.checkOut?.average
                                  ? (
                                      generalProficiency.checkOut.average / 100
                                    ).toFixed(2)
                                  : 0}
                              </Text>
                            </Box>
                          )}
                        </HStack>
                      </Flex>

                      <Box
                        w="100%"
                        bg="#B2B2B2"
                        borderBottomRadius="4xl"
                        borderTopRadius={{ base: "0", md: "4xl" }}
                        borderEndRadius={{ base: "4xl", md: "0" }}
                        p={{ base: 2, md: 4 }}
                        h={{ base: "180px", lg: "220px" }}
                        position="relative"
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={transformToChartData(generalProficiency)}
                            margin={{
                              top: 20,
                              bottom: 5,
                            }}
                          >
                            <XAxis
                              dataKey="name"
                              stroke="black"
                              tickLine={false}
                              fontSize={12}
                              interval={0}
                              angle={0}
                              textAnchor={"middle"}
                              height={30}
                            />
                            <Tooltip
                              cursor={{ fill: "rgba(255, 255, 255, 0.1)" }}
                              contentStyle={{
                                fontSize: "14px",
                                padding: "12px",
                              }}
                            />
                            <Bar
                              dataKey="checkin"
                              fill={generalProficiency.barsColor}
                              barSize={hasCheckout ? 24 : 48}
                            >
                              <LabelList
                                dataKey="checkin"
                                position="top"
                                fill="black"
                                style={{ fontSize: "12px" }}
                              />
                            </Bar>
                            {hasCheckout && (
                              <Bar
                                dataKey="checkout"
                                fill="#00B0F0"
                                barSize={24}
                              >
                                <LabelList
                                  dataKey="checkout"
                                  position="top"
                                  fill="black"
                                />
                              </Bar>
                            )}
                          </BarChart>
                        </ResponsiveContainer>
                      </Box>
                    </Stack>
                  </Box>
                );
              })()}

              {/* Charts for each dimension */}
              {reportData.averageDimensions.map((dimension, index) => {
                const hasCheckout = hasCheckoutData(dimension);
                return (
                  <Box key={index} px={{ base: 3, md: 6 }} w="full">
                    <Stack
                      w={"100%"}
                      bg={dimension.bgColor}
                      borderRadius="4xl"
                      gap={0}
                      align="stretch"
                      flexDirection={{ base: "column", lg: "row" }}
                    >
                      <Flex
                        w="100%"
                        color="white"
                        justify={{ base: "center", lg: "flex-start" }}
                        align="center"
                        gap={{ base: 2, sm: 4 }}
                        py={{ base: 2, lg: 0 }}
                        px={{ base: 0, lg: 6 }}
                      >
                        <HStack>
                          <Text fontSize={{ base: "sm", lg: "lg" }}>
                            {capitalizeSentence(dimension.name)} Média:
                          </Text>
                          <Box
                            border="1px solid white"
                            borderRadius="md"
                            px={1}
                            py={1}
                          >
                            <Text fontSize={{ base: "sm", lg: "lg" }}>
                              {(dimension.checkIn.average / 100).toFixed(2)}
                            </Text>
                          </Box>
                          {hasCheckout && (
                            <Box
                              border="1px solid white"
                              borderRadius="md"
                              px={1}
                              py={1}
                            >
                              <Text fontSize={{ base: "sm", lg: "lg" }}>
                                {dimension.checkOut?.average
                                  ? (dimension.checkOut.average / 100).toFixed(
                                      2
                                    )
                                  : 0}
                              </Text>
                            </Box>
                          )}
                        </HStack>
                      </Flex>

                      <Box
                        w="100%"
                        bg="#B2B2B2"
                        borderBottomRadius="4xl"
                        borderTopRadius={{ base: "0", md: "4xl" }}
                        borderEndRadius={{ base: "4xl", md: "0" }}
                        p={{ base: 2, md: 4 }}
                        h={{ base: "180px", lg: "220px" }}
                        position="relative"
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={transformToChartData(dimension)}
                            margin={{
                              top: 20,
                              bottom: 5,
                            }}
                          >
                            <XAxis
                              dataKey="name"
                              stroke="black"
                              tickLine={false}
                              fontSize={12}
                              interval={0}
                              angle={0}
                              textAnchor={"middle"}
                              height={30}
                            />
                            <Tooltip
                              cursor={{ fill: "rgba(255, 255, 255, 0.1)" }}
                              contentStyle={{
                                fontSize: "14px",
                                padding: "12px",
                              }}
                            />
                            <Bar
                              dataKey="checkin"
                              fill={dimension.barsColor}
                              barSize={hasCheckout ? 24 : 48}
                            >
                              <LabelList
                                dataKey="checkin"
                                position="top"
                                fill="black"
                                style={{ fontSize: "12px" }}
                              />
                            </Bar>
                            {hasCheckout && (
                              <Bar
                                dataKey="checkout"
                                fill="#00B0F0"
                                barSize={24}
                              >
                                <LabelList
                                  dataKey="checkout"
                                  position="top"
                                  fill="black"
                                />
                              </Bar>
                            )}
                          </BarChart>
                        </ResponsiveContainer>
                      </Box>
                    </Stack>
                  </Box>
                );
              })}

              {/* Variable Charts by Dimension and Foundation - Single Result View */}
              {reportData.averageFoundations &&
                reportData.averageFoundations.map((foundationData, index) => {
                  const foundationVariables = reportData.variables.filter(
                    (variable) => variable.foundation === foundationData.name
                  );
                  const hasCheckout = hasCheckoutData(foundationData);
                  return (
                    <VStack
                      key={`${foundationData.dimension}-${foundationData.name}-${index}`}
                      gap={4}
                      w="full"
                    >
                      {/* Dimension Title */}
                      <Box px={{ base: 3, md: 6 }} mb={12} w={"full"}>
                        <HStack
                          w={"full"}
                          justifyContent={"flex-start"}
                          alignItems={"flex-end"}
                          gap={16}
                        >
                          <Image
                            src="/images/logoBancoABC.svg"
                            alt="Banco ABC"
                            w={{ base: "70px", lg: "100px" }}
                            h="auto"
                          />
                          <Text fontSize={{ base: "20px", lg: "50px" }}>
                            {capitalizeSentence(foundationData.dimension)}
                          </Text>
                        </HStack>
                      </Box>

                      {/* Foundation Average Chart */}
                      <Box px={{ base: 3, md: 6 }} w="full">
                        <Stack
                          w={"100%"}
                          bg={darkenColor(foundationData.bgColor, 30)}
                          borderRadius="4xl"
                          gap={0}
                          align="stretch"
                          flexDirection={{ base: "column", lg: "row" }}
                        >
                          <Flex
                            w="100%"
                            color="white"
                            justify={{
                              base: "center",
                              lg: "flex-start",
                            }}
                            align="center"
                            gap={{ base: 2, sm: 4 }}
                            py={{ base: 2, lg: 0 }}
                            px={{ base: 0, lg: 6 }}
                          >
                            <HStack>
                              <Text fontSize={{ base: "sm", lg: "lg" }}>
                                {capitalizeSentence(foundationData.name)} Média:
                              </Text>
                              <Box
                                border="1px solid white"
                                borderRadius="md"
                                px={1}
                                py={1}
                              >
                                <Text fontSize={{ base: "sm", lg: "lg" }}>
                                  {(
                                    foundationData.checkIn.average / 100
                                  ).toFixed(2)}
                                </Text>
                              </Box>
                              {hasCheckout && (
                                <Box
                                  border="1px solid white"
                                  borderRadius="md"
                                  px={1}
                                  py={1}
                                >
                                  <Text fontSize={{ base: "sm", lg: "lg" }}>
                                    {foundationData.checkOut?.average
                                      ? (
                                          foundationData.checkOut.average / 100
                                        ).toFixed(2)
                                      : 0}
                                  </Text>
                                </Box>
                              )}
                            </HStack>
                          </Flex>

                          <Box
                            w="100%"
                            bg="#B2B2B2"
                            borderBottomRadius="4xl"
                            borderTopRadius={{ base: "0", md: "4xl" }}
                            borderEndRadius={{ base: "4xl", md: "0" }}
                            p={{ base: 2, md: 4 }}
                            h={{ base: "180px", lg: "220px" }}
                            position="relative"
                          >
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={transformToChartData(foundationData)}
                                margin={{
                                  top: 20,
                                  bottom: 5,
                                }}
                              >
                                <XAxis
                                  dataKey="name"
                                  stroke="black"
                                  tickLine={false}
                                  fontSize={12}
                                  interval={0}
                                  angle={0}
                                  textAnchor={"middle"}
                                  height={30}
                                />
                                <Tooltip
                                  cursor={{
                                    fill: "rgba(255, 255, 255, 0.1)",
                                  }}
                                  contentStyle={{
                                    fontSize: "14px",
                                    padding: "12px",
                                  }}
                                />
                                <Bar
                                  dataKey="checkin"
                                  fill={foundationData.barsColor}
                                  barSize={hasCheckout ? 24 : 48}
                                >
                                  <LabelList
                                    dataKey="checkin"
                                    position="top"
                                    fill="black"
                                    style={{ fontSize: "12px" }}
                                  />
                                </Bar>
                                {hasCheckout && (
                                  <Bar
                                    dataKey="checkout"
                                    fill="#00B0F0"
                                    barSize={24}
                                  >
                                    <LabelList
                                      dataKey="checkout"
                                      position="top"
                                      fill="black"
                                    />
                                  </Bar>
                                )}
                              </BarChart>
                            </ResponsiveContainer>
                          </Box>
                        </Stack>
                      </Box>

                      {/* Individual Variable Charts */}
                      {foundationVariables.map((variable, variableIndex) => {
                        const hasCheckout = hasCheckoutData(variable);
                        return (
                          <Box
                            key={variableIndex}
                            px={{ base: 3, md: 6 }}
                            w="full"
                          >
                            <Stack
                              w={"100%"}
                              bg={variable.bgColor}
                              borderRadius="4xl"
                              gap={0}
                              align="stretch"
                              flexDirection={{
                                base: "column",
                                lg: "row",
                              }}
                            >
                              <Flex
                                w="100%"
                                color="white"
                                justify={{
                                  base: "center",
                                  lg: "flex-start",
                                }}
                                align="center"
                                gap={{ base: 2, sm: 4 }}
                                py={{ base: 2, lg: 0 }}
                                px={{ base: 0, lg: 6 }}
                              >
                                <HStack>
                                  <Text fontSize={{ base: "sm", lg: "lg" }}>
                                    {capitalizeSentence(variable.name)} Média:
                                  </Text>
                                  <Box
                                    border="1px solid white"
                                    borderRadius="md"
                                    px={1}
                                    py={1}
                                  >
                                    <Text
                                      fontSize={{
                                        base: "sm",
                                        lg: "lg",
                                      }}
                                    >
                                      {(variable.checkIn.average / 100).toFixed(
                                        2
                                      )}
                                    </Text>
                                  </Box>
                                  {hasCheckout && (
                                    <Box
                                      border="1px solid white"
                                      borderRadius="md"
                                      px={1}
                                      py={1}
                                    >
                                      <Text
                                        fontSize={{
                                          base: "sm",
                                          lg: "lg",
                                        }}
                                      >
                                        {variable.checkOut?.average
                                          ? (
                                              variable.checkOut.average / 100
                                            ).toFixed(2)
                                          : 0}
                                      </Text>
                                    </Box>
                                  )}
                                </HStack>
                              </Flex>

                              <Box
                                w="100%"
                                bg="#B2B2B2"
                                borderBottomRadius="4xl"
                                borderTopRadius={{
                                  base: "0",
                                  md: "4xl",
                                }}
                                borderEndRadius={{
                                  base: "4xl",
                                  md: "0",
                                }}
                                p={{ base: 2, md: 4 }}
                                h={{ base: "180px", lg: "220px" }}
                                position="relative"
                              >
                                <ResponsiveContainer width="100%" height="100%">
                                  <BarChart
                                    data={transformToChartData(variable)}
                                    margin={{
                                      top: 20,
                                      bottom: 5,
                                    }}
                                  >
                                    <XAxis
                                      dataKey="name"
                                      stroke="black"
                                      tickLine={false}
                                      fontSize={12}
                                      interval={0}
                                      angle={0}
                                      textAnchor={"middle"}
                                      height={30}
                                    />
                                    <Tooltip
                                      cursor={{
                                        fill: "rgba(255, 255, 255, 0.1)",
                                      }}
                                      contentStyle={{
                                        fontSize: "14px",
                                        padding: "12px",
                                      }}
                                    />
                                    <Bar
                                      dataKey="checkin"
                                      fill={variable.barsColor}
                                      barSize={hasCheckout ? 24 : 48}
                                    >
                                      <LabelList
                                        dataKey="checkin"
                                        position="top"
                                        fill="black"
                                        style={{ fontSize: "12px" }}
                                      />
                                    </Bar>
                                    {hasCheckout && (
                                      <Bar
                                        dataKey="checkout"
                                        fill="#00B0F0"
                                        barSize={24}
                                      >
                                        <LabelList
                                          dataKey="checkout"
                                          position="top"
                                          fill="black"
                                        />
                                      </Bar>
                                    )}
                                  </BarChart>
                                </ResponsiveContainer>
                              </Box>
                            </Stack>
                          </Box>
                        );
                      })}
                    </VStack>
                  );
                })}
            </VStack>
          </Box>
        ) : (
          <Box w="100%" position="relative" overflow="hidden" flexShrink={0}>
            <VStack w="100%" gap={8} align="stretch">
              <Stack
                gap={8}
                alignContent={"start"}
                flexDirection={{ base: "column", lg: "row" }}
                px={6}
              >
                <Image
                  src="/images/logoBancoABC.svg"
                  alt="Banco ABC"
                  w={{ base: "70px", lg: "100px" }}
                  h="auto"
                />
                <Stack
                  gap={4}
                  w={"auto"}
                  flex={1}
                  flexDirection={{ base: "column", lg: "row" }}
                >
                  <Box
                    bgColor={"gray.300"}
                    color={"black"}
                    borderRadius={"lg"}
                    py={3}
                    px={6}
                  >
                    <Text color={"black"} fontSize={{ base: "xs", lg: "md" }}>
                      Check-in
                    </Text>
                    <Text color={"black"} fontSize={{ base: "xs", lg: "md" }}>
                      Quantidade de elegíveis à responder:{" "}
                      {reportData?.respondents.checkIn.quantityAvailable}
                    </Text>
                    <Text color={"black"} fontSize={{ base: "xs", lg: "md" }}>
                      Quantidade de respondentes:{" "}
                      {reportData?.respondents.checkIn.quantityAnswered}
                    </Text>
                  </Box>
                  {reportHasCheckoutData() && (
                    <Box
                      bgColor={"gray.300"}
                      color={"black"}
                      borderRadius={"lg"}
                      py={3}
                      px={6}
                    >
                      <Text color={"black"} fontSize={{ base: "xs", lg: "md" }}>
                        Check-out
                      </Text>
                      <Text color={"black"} fontSize={{ base: "xs", lg: "md" }}>
                        Quantidade de elegíveis à responder:{" "}
                        {reportData?.respondents.checkOut.quantityAvailable}
                      </Text>
                      <Text color={"black"} fontSize={{ base: "xs", lg: "md" }}>
                        Quantidade de respondentes:{" "}
                        {reportData?.respondents.checkOut.quantityAnswered}
                      </Text>
                    </Box>
                  )}
                </Stack>
              </Stack>
              <Stack
                gap={10}
                w={"100%"}
                px={6}
                align={"center"}
                flexDirection={{ base: "column", lg: "row" }}
              >
                <Image
                  src="/images/mandala.png"
                  alt="Mandala"
                  w={{ base: "300px", lg: "500px" }}
                />
                <Text fontSize={{ base: "md", lg: "3xl" }}>
                  "Este relatório mostra seu nível de proficiência em dois
                  momentos: Check-in, que é o diagnóstico inicial realizado na
                  primeira aplicação do assessment, e Check-out, que será feito
                  após sua participação nos programas de capacitação
                  recomendados. A comparação entre os dois momentos permite
                  acompanhar sua evolução e direcionar seu desenvolvimento com
                  mais precisão."
                </Text>
              </Stack>

              {/* General Proficiency Chart */}
              {(() => {
                // const generalProficiency = calculateGeneralProficiency();
                const generalProficiency = reportData.generalProficiency;
                const hasCheckout = hasCheckoutData(generalProficiency);

                return (
                  <Box px={{ base: 3, md: 6 }} w="full">
                    <Stack
                      w={"100%"}
                      bg={generalProficiency.bgColor}
                      borderRadius="4xl"
                      gap={0}
                      align="stretch"
                      flexDirection={{ base: "column", lg: "row" }}
                    >
                      {/* Cabeçalho */}
                      <Flex
                        flex={{ base: "none", lg: 1.5 }}
                        color="white"
                        justify={{ base: "center", lg: "flex-start" }}
                        align="center"
                        gap={{ base: 2, sm: 4 }}
                        py={{ base: 2, lg: 0 }}
                        px={{ base: 0, lg: 6 }}
                      >
                        <HStack>
                          <Text fontSize={{ base: "sm", lg: "lg" }}>
                            Proficiência Geral:
                          </Text>
                          <Box
                            border="1px solid white"
                            borderRadius="md"
                            px={1}
                            py={1}
                          >
                            <Text fontSize={{ base: "sm", lg: "lg" }}>
                              {generalProficiency.checkIn.average
                                ? (
                                    generalProficiency.checkIn.average / 100
                                  ).toFixed(2)
                                : 0}
                            </Text>
                          </Box>
                          {hasCheckout && (
                            <>
                              <Text fontSize={{ base: "sm", lg: "lg" }}>
                                {" "}
                                /{" "}
                                {generalProficiency.checkOut?.average
                                  ? (
                                      generalProficiency.checkOut.average / 100
                                    ).toFixed(2)
                                  : 0}
                              </Text>
                            </>
                          )}
                        </HStack>
                      </Flex>

                      {/* Área do gráfico porcentagem */}
                      <VStack
                        flex={{ base: "none", lg: 3 }}
                        minWidth={{ lg: "320px" }}
                        bg="#B2B2B2"
                        borderLeftRadius={{ base: "0", lg: "4xl" }}
                        p={{ base: 2, md: 4 }}
                        h={{ base: "180px", sm: "200px", md: "220px" }}
                        position="relative"
                        pr={{ base: "0", lg: "50px" }}
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={transformToPercentageData(generalProficiency)}
                            margin={{ top: 20, bottom: 5, right: 5, left: 5 }}
                          >
                            <XAxis
                              dataKey="name"
                              stroke="black"
                              tickLine={false}
                              fontSize={12}
                              interval={0}
                            />
                            <Tooltip
                              cursor={{ fill: "rgba(255, 255, 255, 0.1)" }}
                              contentStyle={{
                                fontSize: "14px",
                                padding: "12px",
                              }}
                              formatter={(value: any) => [
                                `${Number(value).toFixed(0)}%`,
                                "",
                              ]}
                            />
                            <Bar
                              dataKey="checkin"
                              fill={generalProficiency.barsColor}
                              barSize={hasCheckout ? 24 : 48}
                            >
                              <LabelList
                                dataKey="checkin"
                                position="top"
                                fill="black"
                                formatter={formatLabelAsPercentage}
                              />
                            </Bar>
                            {hasCheckout && (
                              <Bar
                                dataKey="checkout"
                                fill="#00B0F0"
                                barSize={24}
                              >
                                <LabelList
                                  dataKey="checkout"
                                  position="top"
                                  fill="black"
                                  formatter={formatLabelAsPercentage}
                                />
                              </Bar>
                            )}
                          </BarChart>
                        </ResponsiveContainer>
                      </VStack>

                      {/* Área do gráfico valores brutos */}
                      <VStack
                        flex={{ base: "none", lg: 3 }}
                        minWidth={{ lg: "320px" }}
                        bg="#DADADA"
                        borderLeftRadius={{ base: "0", lg: "4xl" }}
                        p={{ base: 2, md: 4 }}
                        h={{ base: "180px", sm: "200px", md: "220px" }}
                        position="relative"
                        ml={{ base: "0", lg: "-30px" }}
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={transformToChartData(generalProficiency)}
                            margin={{ top: 20, bottom: 5, right: 5, left: 5 }}
                          >
                            <XAxis
                              dataKey="name"
                              stroke="black"
                              tickLine={false}
                              fontSize={12}
                              interval={0}
                            />
                            <Tooltip
                              cursor={{ fill: "rgba(255, 255, 255, 0.1)" }}
                            />
                            <Bar
                              dataKey="checkin"
                              fill={generalProficiency.barsColor}
                              barSize={hasCheckout ? 24 : 48}
                            >
                              <LabelList
                                dataKey="checkin"
                                position="top"
                                fill="black"
                                style={{ fontSize: "12px" }}
                              />
                            </Bar>
                            {hasCheckout && (
                              <Bar
                                dataKey="checkout"
                                fill="#00B0F0"
                                barSize={24}
                              >
                                <LabelList
                                  dataKey="checkout"
                                  position="top"
                                  fill="black"
                                />
                              </Bar>
                            )}
                          </BarChart>
                        </ResponsiveContainer>
                      </VStack>
                    </Stack>
                  </Box>
                );
              })()}

              {/* Charts for each dimension */}
              {reportData.averageDimensions.map((dimension, index) => {
                const hasCheckout = hasCheckoutData(dimension);
                return (
                  <Box key={index} px={{ base: 3, md: 6 }} w="full">
                    <Stack
                      w={"100%"}
                      bg={dimension.bgColor}
                      borderRadius="4xl"
                      gap={0}
                      align="stretch"
                      flexDirection={{ base: "column", lg: "row" }}
                    >
                      {/* Cabeçalho */}
                      <Flex
                        flex={{ base: "none", lg: 1.5 }}
                        color="white"
                        justify={{ base: "center", lg: "flex-start" }}
                        align="center"
                        gap={{ base: 2, sm: 4 }}
                        py={{ base: 2, lg: 0 }}
                        px={{ base: 0, lg: 6 }}
                      >
                        <HStack>
                          <Text fontSize={{ base: "sm", lg: "lg" }}>
                            {capitalizeSentence(dimension.name)} Média:
                          </Text>
                          <Box
                            border="1px solid white"
                            borderRadius="md"
                            px={1}
                            py={1}
                          >
                            <Text fontSize={{ base: "sm", lg: "lg" }}>
                              {dimension.checkIn.average
                                ? (dimension.checkIn.average / 100).toFixed(2)
                                : 0}
                            </Text>
                          </Box>
                          {hasCheckout && (
                            <>
                              <Text fontSize={{ base: "sm", lg: "lg" }}>
                                {" "}
                                /{" "}
                                {dimension.checkOut?.average
                                  ? (dimension.checkOut.average / 100).toFixed(
                                      2
                                    )
                                  : 0}
                              </Text>
                            </>
                          )}
                        </HStack>
                      </Flex>

                      {/* Área do gráfico porcentagem */}
                      <VStack
                        flex={{ base: "none", lg: 3 }}
                        minWidth={{ lg: "320px" }}
                        bg="#B2B2B2"
                        borderLeftRadius={{ base: "0", lg: "4xl" }} // "lg" em vez de "md" para consistência
                        p={{ base: 2, md: 4 }}
                        h={{ base: "180px", sm: "200px", md: "220px" }}
                        position="relative"
                        pr={{ base: "0", lg: "50px" }}
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={transformToPercentageData(dimension)}
                            margin={{ top: 20, bottom: 5, right: 5, left: 5 }} // Adicionei margens laterais
                          >
                            <XAxis
                              dataKey="name"
                              stroke="black"
                              tickLine={false}
                              fontSize={12}
                              interval={0}
                            />
                            <Tooltip
                              cursor={{ fill: "rgba(255, 255, 255, 0.1)" }}
                              formatter={formatLabelAsPercentage}
                            />
                            <Bar
                              dataKey="checkin"
                              fill={dimension.barsColor}
                              barSize={hasCheckout ? 24 : 48}
                            >
                              <LabelList
                                dataKey="checkin"
                                position="top"
                                fill="black"
                                style={{ fontSize: "12px" }}
                                formatter={formatLabelAsPercentage}
                              />
                            </Bar>
                            {hasCheckout && (
                              <Bar
                                dataKey="checkout"
                                fill="#00B0F0"
                                barSize={24}
                              >
                                <LabelList
                                  dataKey="checkout"
                                  position="top"
                                  fill="black"
                                  formatter={formatLabelAsPercentage}
                                />
                              </Bar>
                            )}
                          </BarChart>
                        </ResponsiveContainer>
                      </VStack>

                      {/* Área do gráfico valores brutos */}
                      <VStack
                        flex={{ base: "none", lg: 3 }}
                        minWidth={{ lg: "320px" }}
                        bg="#DADADA"
                        borderLeftRadius={{ base: "0", lg: "4xl" }}
                        p={{ base: 2, md: 4 }}
                        h={{ base: "180px", sm: "200px", md: "220px" }}
                        position="relative"
                        ml={{ base: "0", lg: "-30px" }}
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={transformToChartData(dimension)}
                            margin={{ top: 20, bottom: 5, right: 5, left: 5 }}
                          >
                            <XAxis
                              dataKey="name"
                              stroke="black"
                              tickLine={false}
                              fontSize={12}
                              interval={0}
                            />
                            <Tooltip
                              cursor={{ fill: "rgba(255, 255, 255, 0.1)" }}
                            />
                            <Bar
                              dataKey="checkin"
                              fill={dimension.barsColor}
                              barSize={hasCheckout ? 24 : 48}
                            >
                              <LabelList
                                dataKey="checkin"
                                position="top"
                                fill="black"
                                style={{ fontSize: "12px" }}
                              />
                            </Bar>
                            {hasCheckout && (
                              <Bar
                                dataKey="checkout"
                                fill="#00B0F0"
                                barSize={24}
                              >
                                <LabelList
                                  dataKey="checkout"
                                  position="top"
                                  fill="black"
                                />
                              </Bar>
                            )}
                          </BarChart>
                        </ResponsiveContainer>
                      </VStack>
                    </Stack>
                  </Box>
                );
              })}

              {/* Variable Charts by Dimension and Foundation */}
              {reportData.averageFoundations &&
                reportData.averageFoundations.map((foundationData, index) => {
                  const foundationVariables = reportData.variables.filter(
                    (variable) => variable.foundation === foundationData.name
                  );
                  const hasCheckout = hasCheckoutData(foundationData);

                  return (
                    <VStack
                      key={`${foundationData.dimension}-${foundationData.name}-${index}`}
                      gap={4}
                      w="full"
                    >
                      {/* Dimension Title */}
                      <Box px={{ base: 3, md: 6 }} mb={12} w={"full"}>
                        <HStack
                          w={"full"}
                          justifyContent={"flex-start"}
                          alignItems={"flex-end"}
                          gap={16}
                        >
                          <Image
                            src="/images/logoBancoABC.svg"
                            alt="Banco ABC"
                            w={{ base: "70px", lg: "100px" }}
                            h="auto"
                          />
                          <Text fontSize={{ base: "20px", lg: "50px" }}>
                            {capitalizeSentence(foundationData.dimension)}
                          </Text>
                        </HStack>
                      </Box>

                      {/* Foundation Average Chart */}
                      <Box px={{ base: 3, md: 6 }} w="full">
                        <Stack
                          w={"100%"}
                          bg={darkenColor(foundationData.bgColor, 30)}
                          borderRadius="4xl"
                          gap={0}
                          align="stretch"
                          flexDirection={{ base: "column", lg: "row" }}
                        >
                          <Flex
                            flex={{ base: "none", lg: 1.5 }}
                            color="white"
                            justify={{
                              base: "center",
                              lg: "flex-start",
                            }}
                            align="center"
                            gap={{ base: 2, sm: 4 }}
                            py={{ base: 2, lg: 0 }}
                            px={{ base: 0, lg: 6 }}
                          >
                            <HStack>
                              <Text fontSize={{ base: "sm", lg: "lg" }}>
                                {capitalizeSentence(foundationData.name)} Média:
                              </Text>
                              <Box
                                border="1px solid white"
                                borderRadius="md"
                                px={1}
                                py={1}
                              >
                                <Text fontSize={{ base: "sm", lg: "lg" }}>
                                  {(
                                    foundationData.checkIn.average / 100
                                  ).toFixed(2)}
                                </Text>
                              </Box>
                              {hasCheckout && (
                                <>
                                  <Text fontSize={{ base: "sm", lg: "lg" }}>
                                    {" "}
                                    /{" "}
                                    {foundationData.checkOut?.average
                                      ? (
                                          foundationData.checkOut.average / 100
                                        ).toFixed(2)
                                      : 0}
                                  </Text>
                                </>
                              )}
                            </HStack>
                          </Flex>

                          {/* Percentage Chart */}
                          <VStack
                            flex={{ base: "none", lg: 3 }}
                            minWidth={{ lg: "320px" }}
                            bg="#B2B2B2"
                            borderLeftRadius={{ base: "0", lg: "4xl" }}
                            p={{ base: 2, md: 4 }}
                            h={{
                              base: "180px",
                              sm: "200px",
                              md: "220px",
                            }}
                            position="relative"
                            pr={{ base: "0", lg: "50px" }}
                          >
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={transformToPercentageData(foundationData)}
                                margin={{
                                  top: 20,
                                  bottom: 5,
                                  right: 5,
                                  left: 5,
                                }}
                              >
                                <XAxis
                                  dataKey="name"
                                  stroke="black"
                                  tickLine={false}
                                  fontSize={12}
                                  interval={0}
                                />
                                <Tooltip
                                  cursor={{
                                    fill: "rgba(255, 255, 255, 0.1)",
                                  }}
                                  contentStyle={{
                                    fontSize: "14px",
                                    padding: "12px",
                                  }}
                                  formatter={(value: any) => [
                                    `${Number(value).toFixed(0)}%`,
                                    "",
                                  ]}
                                />
                                <Bar
                                  dataKey="checkin"
                                  fill={foundationData.barsColor}
                                  barSize={hasCheckout ? 24 : 48}
                                >
                                  <LabelList
                                    dataKey="checkin"
                                    position="top"
                                    fill="black"
                                    formatter={formatLabelAsPercentage}
                                  />
                                </Bar>
                                {hasCheckout && (
                                  <Bar
                                    dataKey="checkout"
                                    fill="#00B0F0"
                                    barSize={24}
                                  >
                                    <LabelList
                                      dataKey="checkout"
                                      position="top"
                                      fill="black"
                                      formatter={formatLabelAsPercentage}
                                    />
                                  </Bar>
                                )}
                              </BarChart>
                            </ResponsiveContainer>
                          </VStack>

                          {/* Raw Values Chart */}
                          <VStack
                            flex={{ base: "none", lg: 3 }}
                            minWidth={{ lg: "320px" }}
                            bg="#DADADA"
                            borderLeftRadius={{ base: "0", lg: "4xl" }}
                            p={{ base: 2, md: 4 }}
                            h={{
                              base: "180px",
                              sm: "200px",
                              md: "220px",
                            }}
                            position="relative"
                            ml={{ base: "0", lg: "-30px" }}
                          >
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={transformToChartData(foundationData)}
                                margin={{
                                  top: 20,
                                  bottom: 5,
                                  right: 5,
                                  left: 5,
                                }}
                              >
                                <XAxis
                                  dataKey="name"
                                  stroke="black"
                                  tickLine={false}
                                  fontSize={12}
                                  interval={0}
                                />
                                <Tooltip
                                  cursor={{
                                    fill: "rgba(255, 255, 255, 0.1)",
                                  }}
                                />
                                <Bar
                                  dataKey="checkin"
                                  fill={foundationData.barsColor}
                                  barSize={hasCheckout ? 24 : 48}
                                >
                                  <LabelList
                                    dataKey="checkin"
                                    position="top"
                                    fill="black"
                                    style={{ fontSize: "12px" }}
                                  />
                                </Bar>
                                {hasCheckout && (
                                  <Bar
                                    dataKey="checkout"
                                    fill="#00B0F0"
                                    barSize={24}
                                  >
                                    <LabelList
                                      dataKey="checkout"
                                      position="top"
                                      fill="black"
                                    />
                                  </Bar>
                                )}
                              </BarChart>
                            </ResponsiveContainer>
                          </VStack>
                        </Stack>
                      </Box>

                      {/* Individual Variable Charts */}
                      {foundationVariables.map((variable, variableIndex) => {
                        const hasCheckout = hasCheckoutData(variable);
                        return (
                          <Box
                            key={variableIndex}
                            px={{ base: 3, md: 6 }}
                            w="full"
                          >
                            <Stack
                              w={"100%"}
                              bg={variable.bgColor}
                              borderRadius="4xl"
                              gap={0}
                              align="stretch"
                              flexDirection={{
                                base: "column",
                                lg: "row",
                              }}
                            >
                              <Flex
                                flex={{ base: "none", lg: 1.5 }}
                                color="white"
                                justify={{
                                  base: "center",
                                  lg: "flex-start",
                                }}
                                align="center"
                                gap={{ base: 2, sm: 4 }}
                                py={{ base: 2, lg: 0 }}
                                px={{ base: 0, lg: 6 }}
                              >
                                <HStack>
                                  <Text fontSize={{ base: "sm", lg: "lg" }}>
                                    {capitalizeSentence(variable.name)} Média:
                                  </Text>
                                  <Box
                                    border="1px solid white"
                                    borderRadius="md"
                                    px={1}
                                    py={1}
                                  >
                                    <Text
                                      fontSize={{
                                        base: "sm",
                                        lg: "lg",
                                      }}
                                    >
                                      {(variable.checkIn.average / 100).toFixed(
                                        2
                                      )}
                                    </Text>
                                  </Box>
                                  {hasCheckout && (
                                    <>
                                      <Text
                                        fontSize={{
                                          base: "sm",
                                          lg: "lg",
                                        }}
                                      >
                                        {" "}
                                        /{" "}
                                        {variable.checkOut?.average
                                          ? (
                                              variable.checkOut.average / 100
                                            ).toFixed(2)
                                          : 0}
                                      </Text>
                                    </>
                                  )}
                                </HStack>
                              </Flex>

                              {/* Percentage Chart */}
                              <VStack
                                flex={{ base: "none", lg: 3 }}
                                minWidth={{ lg: "320px" }}
                                bg="#B2B2B2"
                                borderLeftRadius={{
                                  base: "0",
                                  lg: "4xl",
                                }}
                                p={{ base: 2, md: 4 }}
                                h={{
                                  base: "180px",
                                  sm: "200px",
                                  md: "220px",
                                }}
                                position="relative"
                                pr={{ base: "0", lg: "50px" }}
                              >
                                <ResponsiveContainer width="100%" height="100%">
                                  <BarChart
                                    data={transformToPercentageData(variable)}
                                    margin={{
                                      top: 20,
                                      bottom: 5,
                                      right: 5,
                                      left: 5,
                                    }}
                                  >
                                    <XAxis
                                      dataKey="name"
                                      stroke="black"
                                      tickLine={false}
                                      fontSize={12}
                                      interval={0}
                                    />
                                    <Tooltip
                                      cursor={{
                                        fill: "rgba(255, 255, 255, 0.1)",
                                      }}
                                      contentStyle={{
                                        fontSize: "14px",
                                        padding: "12px",
                                      }}
                                      formatter={(value: any) => [
                                        `${Number(value).toFixed(0)}%`,
                                        "",
                                      ]}
                                    />
                                    <Bar
                                      dataKey="checkin"
                                      fill={variable.barsColor}
                                      barSize={hasCheckout ? 24 : 48}
                                    >
                                      <LabelList
                                        dataKey="checkin"
                                        position="top"
                                        fill="black"
                                        formatter={formatLabelAsPercentage}
                                      />
                                    </Bar>
                                    {hasCheckout && (
                                      <Bar
                                        dataKey="checkout"
                                        fill="#00B0F0"
                                        barSize={24}
                                      >
                                        <LabelList
                                          dataKey="checkout"
                                          position="top"
                                          fill="black"
                                          formatter={formatLabelAsPercentage}
                                        />
                                      </Bar>
                                    )}
                                  </BarChart>
                                </ResponsiveContainer>
                              </VStack>

                              {/* Raw Values Chart */}
                              <VStack
                                flex={{ base: "none", lg: 3 }}
                                minWidth={{ lg: "320px" }}
                                bg="#DADADA"
                                borderLeftRadius={{
                                  base: "0",
                                  lg: "4xl",
                                }}
                                p={{ base: 2, md: 4 }}
                                h={{
                                  base: "180px",
                                  sm: "200px",
                                  md: "220px",
                                }}
                                position="relative"
                                ml={{ base: "0", lg: "-30px" }}
                              >
                                <ResponsiveContainer width="100%" height="100%">
                                  <BarChart
                                    data={transformToChartData(variable)}
                                    margin={{
                                      top: 20,
                                      bottom: 5,
                                      right: 5,
                                      left: 5,
                                    }}
                                  >
                                    <XAxis
                                      dataKey="name"
                                      stroke="black"
                                      tickLine={false}
                                      fontSize={12}
                                      interval={0}
                                    />
                                    <Tooltip
                                      cursor={{
                                        fill: "rgba(255, 255, 255, 0.1)",
                                      }}
                                    />
                                    <Bar
                                      dataKey="checkin"
                                      fill={variable.barsColor}
                                      barSize={hasCheckout ? 24 : 48}
                                    >
                                      <LabelList
                                        dataKey="checkin"
                                        position="top"
                                        fill="black"
                                        style={{ fontSize: "12px" }}
                                      />
                                    </Bar>
                                    {hasCheckout && (
                                      <Bar
                                        dataKey="checkout"
                                        fill="#00B0F0"
                                        barSize={24}
                                      >
                                        <LabelList
                                          dataKey="checkout"
                                          position="top"
                                          fill="black"
                                        />
                                      </Bar>
                                    )}
                                  </BarChart>
                                </ResponsiveContainer>
                              </VStack>
                            </Stack>
                          </Box>
                        );
                      })}
                    </VStack>
                  );
                })}
            </VStack>
          </Box>
        )}
      </Box>
    </>
  );
}
