import { useState } from "react";
import { useGenerateReport } from "@/hook/reports/useGenerateReport";
import { useGenerateRespondentReport } from "@/hook/reports/useGenerateRespondentReport";
import { ReportGenerationResponse, SearchMode, Segment } from "../data/types";
import { validateReportSelections, createReportRequest } from "../utils/reportDataCollector";
import { toaster } from "@/components/ui/toaster";

interface UseReportGenerationProps {
  searchMode: SearchMode;
  selectedRounds: Set<string>;
  selectedItems: Set<string>;
  selectedRespondent: string | null;
  hierarchyData: Segment[] | undefined;
}

export function useReportGeneration({
  searchMode,
  selectedRounds,
  selectedItems,
  selectedRespondent,
  hierarchyData,
}: UseReportGenerationProps) {
  const [reportData, setReportData] = useState<ReportGenerationResponse | null>(null);
  const [showResults, setShowResults] = useState(false);

  const generateReportMutation = useGenerateReport();
  const generateRespondentReportMutation = useGenerateRespondentReport();

  // Validation for Generate Report button
  const isGenerateDisabled =
    selectedRounds.size === 0 ||
    (searchMode === "segments" &&
      (!hierarchyData || selectedItems.size === 0)) ||
    (searchMode === "respondent" && !selectedRespondent);

  // Handle report generation
  const handleGenerateReport = async () => {
    if (searchMode === "segments") {
      if (!hierarchyData) {
        toaster.error({
          title: "Erro",
          description: "Dados da hierarquia não carregados",
        });
        return;
      }

      // Validate selections
      const validation = validateReportSelections(
        selectedRounds,
        selectedItems,
        hierarchyData
      );

      if (!validation.isValid) {
        toaster.error({
          title: "Seleção inválida",
          description: validation.message,
        });
        return;
      }

      try {
        // Create request payload
        const requestData = createReportRequest(
          selectedRounds,
          selectedItems,
          hierarchyData
        );

        // Generate report
        const response = await generateReportMutation.mutateAsync(requestData);
        setReportData(response);
        setShowResults(true);
      } catch (error) {
        console.error("Error generating report:", error);
      }
    } else if (searchMode === "respondent") {
      if (!selectedRespondent) {
        toaster.error({
          title: "Respondente não selecionado",
          description: "Selecione um respondente para gerar o relatório.",
        });
        return;
      }

      try {
        // Convert rounds format
        const rounds =
          selectedRounds.has("rodada1") && selectedRounds.has("rodada2")
            ? "both"
            : selectedRounds.has("rodada1")
            ? "checkIn"
            : "checkOut";

        const requestData = {
          rounds: rounds as "checkIn" | "checkOut" | "both",
          respondentSecureId: selectedRespondent,
        };

        // Generate respondent report
        const response = await generateRespondentReportMutation.mutateAsync(
          requestData
        );
        setReportData(response);
        setShowResults(true);
      } catch (error) {
        console.error("Error generating respondent report:", error);
      }
    }
  };

  const resetResults = () => {
    setShowResults(false);
    setReportData(null);
  };

  return {
    reportData,
    showResults,
    isGenerateDisabled,
    isGenerating: generateReportMutation.isPending,
    handleGenerateReport,
    resetResults,
  };
}
