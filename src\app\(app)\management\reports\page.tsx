"use client";
import { useHierarchicalSelection } from "./hooks/useHierarchicalSelection";
import { useRoundSelection } from "./hooks/useRoundSelection";
import { useSearchMode } from "./hooks/useSearchMode";
import { useReportGeneration } from "./hooks/useReportGeneration";
import { ReportsHeader } from "./components/ReportsHeader";
import { ReportsContent } from "./components/ReportsContent";
import { ReportsContainer } from "./components/ReportsContainer";
import { ReportResults } from "./components/ReportResults";
import { useGetHierarchyReports } from "@/hook/reports/useGetHierarchyReports";
import { useGetRespondentsBySearch } from "@/hook/reports/useGetRespondentsBySearch";
import { SearchMode } from "./data/types";

export default function Reports() {
  const { data: hierarchyReportsData, isLoading } = useGetHierarchyReports();

  const {
    searchTerm,
    setSearchTerm,
    searchMode,
    selectedRespondent,
    setSelectedRespondent,
    handleSearchModeChange,
  } = useSearchMode();

  const { data: respondentsData, isLoading: isLoadingRespondents } =
    useGetRespondentsBySearch(
      searchMode === "respondent" && searchTerm.trim() ? searchTerm : undefined
    );

  const {
    selectedItems,
    allSelected,
    handleItemChangeByPath,
    handleAllChange,
    isItemSelectedByPath,
  } = useHierarchicalSelection(hierarchyReportsData?.data || []);

  const { selectedRounds, handleRoundChange } = useRoundSelection();

  const {
    reportData,
    showResults,
    isGenerateDisabled,
    isGenerating,
    handleGenerateReport,
    resetResults,
  } = useReportGeneration({
    searchMode,
    selectedRounds,
    selectedItems,
    selectedRespondent,
    hierarchyData: hierarchyReportsData?.data,
  });

  const handleSearchModeChangeWithReset = (mode: SearchMode) => {
    handleSearchModeChange(mode);
    resetResults();
  };

  return (
    <ReportsContainer isLoading={isLoading || !hierarchyReportsData}>
      {/* Header */}
      <ReportsHeader
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        onGenerateReport={handleGenerateReport}
        isGenerateDisabled={isGenerateDisabled}
        isGenerating={isGenerating}
        searchMode={searchMode}
        onSearchModeChange={handleSearchModeChangeWithReset}
      />

      {/* Main Content */}
      <ReportsContent
        searchMode={searchMode}
        allSelected={allSelected}
        selectedRounds={selectedRounds}
        onAllChange={handleAllChange}
        onRoundChange={handleRoundChange}
        segments={hierarchyReportsData?.data}
        onItemChangeByPath={handleItemChangeByPath}
        isItemSelectedByPath={isItemSelectedByPath}
        selectedRespondent={selectedRespondent}
        onRespondentChange={setSelectedRespondent}
        respondentsData={respondentsData}
        searchTerm={searchTerm}
        isLoadingRespondents={isLoadingRespondents}
      />

      {/* Report Results */}
      {showResults && reportData ? (
        <ReportResults reportData={reportData} searchMode={searchMode} />
      ) : null}
    </ReportsContainer>
  );
}
