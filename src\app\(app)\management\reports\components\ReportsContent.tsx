import { HStack } from "@chakra-ui/react";
import { ReportsFilters } from "./ReportsFilters";
import { SegmentDisplay } from "./SegmentDisplay";
import { RespondentSearch } from "./RespondentSearch";
import { SearchMode, Segment } from "../data/types";
import { UseHierarchicalSelectionReturn, UseRoundSelectionReturn } from "../data/types";

interface ReportsContentProps {
  searchMode: SearchMode;
  allSelected: boolean;
  selectedRounds: Set<string>;
  onAllChange: (checked: boolean) => void;
  onRoundChange: (round: string, checked: boolean) => void;
  segments: Segment[] | undefined;
  onItemChangeByPath: UseHierarchicalSelectionReturn["handleItemChangeByPath"];
  isItemSelectedByPath: UseHierarchicalSelectionReturn["isItemSelectedByPath"];
  selectedRespondent: string | null;
  onRespondentChange: (respondent: string | null) => void;
  respondentsData: any;
  searchTerm: string;
  isLoadingRespondents: boolean;
}

export function ReportsContent({
  searchMode,
  allSelected,
  selectedRounds,
  onAllChange,
  onRoundChange,
  segments,
  onItemChangeByPath,
  isItemSelectedByPath,
  selectedRespondent,
  onRespondentChange,
  respondentsData,
  searchTerm,
  isLoadingRespondents,
}: ReportsContentProps) {
  return (
    <HStack flexDirection={"column"} align="flex-start" gap={8}>
      {/* Controls */}
      <ReportsFilters
        allSelected={searchMode === "segments" ? allSelected : false}
        selectedRounds={selectedRounds}
        onAllChange={
          searchMode === "segments" ? onAllChange : () => {}
        }
        onRoundChange={onRoundChange}
        hideAllOption={searchMode !== "segments"}
      />

      {/* Conditional Content Based on Search Mode */}
      {searchMode === "segments" ? (
        <SegmentDisplay
          segments={segments}
          onItemChangeByPath={onItemChangeByPath}
          isItemSelectedByPath={isItemSelectedByPath}
        />
      ) : (
        <RespondentSearch
          selectedRespondent={selectedRespondent}
          onRespondentChange={onRespondentChange}
          respondentsData={respondentsData}
          searchTerm={searchTerm}
          isLoading={isLoadingRespondents}
        />
      )}
    </HStack>
  );
}
