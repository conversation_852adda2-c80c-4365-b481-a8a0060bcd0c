import { <PERSON><PERSON>, Spinner, VStack } from "@chakra-ui/react";
import { ReactNode } from "react";

interface ReportsContainerProps {
  isLoading: boolean;
  children: ReactNode;
}

export function ReportsContainer({ isLoading, children }: ReportsContainerProps) {
  if (isLoading) {
    return (
      <Flex flex={1} justify="center" align="center">
        <Spinner size="xl" />
      </Flex>
    );
  }

  return (
    <Flex
      flex={1}
      position="relative"
      mt={10}
      overflowX="auto"
      paddingRight={{ base: 0, lg: 12 }}
    >
      <VStack w="100%" gap={6} align="stretch">
        <VStack p={6} align="stretch">
          {children}
        </VStack>
      </VStack>
    </Flex>
  );
}
